/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.23.0+4000"}
 */

/**
 * Import the modules used in this configuration.
 */
const ADC12  = scripting.addModule("/ti/driverlib/ADC12", {}, false);
const ADC121 = ADC12.addInstance();
const GPIO   = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1  = GPIO.addInstance();
const GPIO2  = GPIO.addInstance();
const GPIO3  = GPIO.addInstance();
const GPIO4  = GPIO.addInstance();
const GPIO5  = GPIO.addInstance();
const GPIO6  = GPIO.addInstance();
const GPIO7  = GPIO.addInstance();
const GPIO8  = GPIO.addInstance();
const GPIO9  = GPIO.addInstance();
const GPIO10 = GPIO.addInstance();
const PWM    = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1   = PWM.addInstance();
const PWM2   = PWM.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");
const TIMER  = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1 = TIMER.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const divider7       = system.clockTree["PLL_PDIV"];
divider7.divideValue = 2;

const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 5;

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL2X";

ADC121.$name                             = "ADC1";
ADC121.sampClkDiv                        = "DL_ADC12_CLOCK_DIVIDE_8";
ADC121.powerDownMode                     = "DL_ADC12_POWER_DOWN_MODE_MANUAL";
ADC121.sampleTime0                       = "125us";
ADC121.adcMem0_name                      = "ADC_Channel0";
ADC121.peripheral.$assign                = "ADC0";
ADC121.peripheral.adcPin0.$assign        = "PA27";
ADC121.adcPin0Config.$name               = "ti_driverlib_gpio_GPIOPinGeneric0";
ADC121.adcPin0Config.hideOutputInversion = scripting.forceWrite(false);

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO1.$name                         = "GPIO_OLED";
GPIO1.associatedPins.create(2);
GPIO1.associatedPins[0].$name       = "PIN_SCL";
GPIO1.associatedPins[0].pin.$assign = "PB12";
GPIO1.associatedPins[1].$name       = "PIN_SDA";
GPIO1.associatedPins[1].pin.$assign = "PB4";

GPIO2.$name                         = "GPIO_LED";
GPIO2.associatedPins[0].$name       = "PIN_led";
GPIO2.associatedPins[0].pin.$assign = "PA7";

GPIO3.$name                         = "GPIO_IN";
GPIO3.associatedPins.create(4);
GPIO3.associatedPins[0].$name       = "PIN_AIN1";
GPIO3.associatedPins[0].pin.$assign = "PA15";
GPIO3.associatedPins[1].$name       = "PIN_AIN2";
GPIO3.associatedPins[1].pin.$assign = "PA16";
GPIO3.associatedPins[2].$name       = "PIN_BIN1";
GPIO3.associatedPins[2].pin.$assign = "PA17";
GPIO3.associatedPins[3].$name       = "PIN_BIN2";
GPIO3.associatedPins[3].pin.$assign = "PA24";

GPIO4.$name                              = "MODE";
GPIO4.associatedPins[0].$name            = "ctrl";
GPIO4.associatedPins[0].direction        = "INPUT";
GPIO4.associatedPins[0].internalResistor = "PULL_UP";
GPIO4.associatedPins[0].pin.$assign      = "PA25";

GPIO5.$name                              = "BEGIN";
GPIO5.associatedPins[0].$name            = "GO";
GPIO5.associatedPins[0].direction        = "INPUT";
GPIO5.associatedPins[0].internalResistor = "PULL_UP";
GPIO5.associatedPins[0].pin.$assign      = "PA9";

GPIO6.$name                         = "GPIO_STBY";
GPIO6.associatedPins[0].$name       = "PIN_STBY";
GPIO6.associatedPins[0].pin.$assign = "PB9";

GPIO7.$name                               = "ENCODER";
GPIO7.port                                = "PORTB";
GPIO7.associatedPins.create(4);
GPIO7.associatedPins[0].$name             = "E1A";
GPIO7.associatedPins[0].direction         = "INPUT";
GPIO7.associatedPins[0].interruptEn       = true;
GPIO7.associatedPins[0].polarity          = "RISE";
GPIO7.associatedPins[0].interruptPriority = "1";
GPIO7.associatedPins[0].pin.$assign       = "PB8";
GPIO7.associatedPins[1].$name             = "E1B";
GPIO7.associatedPins[1].direction         = "INPUT";
GPIO7.associatedPins[1].interruptEn       = true;
GPIO7.associatedPins[1].polarity          = "RISE";
GPIO7.associatedPins[1].interruptPriority = "1";
GPIO7.associatedPins[1].pin.$assign       = "PB15";
GPIO7.associatedPins[2].$name             = "E2A";
GPIO7.associatedPins[2].direction         = "INPUT";
GPIO7.associatedPins[2].interruptEn       = true;
GPIO7.associatedPins[2].polarity          = "RISE";
GPIO7.associatedPins[2].interruptPriority = "1";
GPIO7.associatedPins[2].pin.$assign       = "PB17";
GPIO7.associatedPins[3].$name             = "E2B";
GPIO7.associatedPins[3].direction         = "INPUT";
GPIO7.associatedPins[3].interruptEn       = true;
GPIO7.associatedPins[3].polarity          = "RISE";
GPIO7.associatedPins[3].interruptPriority = "1";

GPIO8.$name                              = "PID";
GPIO8.associatedPins.create(3);
GPIO8.associatedPins[0].$name            = "KP";
GPIO8.associatedPins[0].direction        = "INPUT";
GPIO8.associatedPins[0].internalResistor = "PULL_UP";
GPIO8.associatedPins[0].pin.$assign      = "PA8";
GPIO8.associatedPins[1].$name            = "KI";
GPIO8.associatedPins[1].direction        = "INPUT";
GPIO8.associatedPins[1].internalResistor = "PULL_UP";
GPIO8.associatedPins[1].pin.$assign      = "PA26";
GPIO8.associatedPins[2].$name            = "KD";
GPIO8.associatedPins[2].direction        = "INPUT";
GPIO8.associatedPins[2].internalResistor = "PULL_UP";
GPIO8.associatedPins[2].pin.$assign      = "PA22";

GPIO9.$name                              = "Change";
GPIO9.associatedPins[0].direction        = "INPUT";
GPIO9.associatedPins[0].internalResistor = "PULL_UP";
GPIO9.associatedPins[0].$name            = "all";
GPIO9.associatedPins[0].pin.$assign      = "PB19";

GPIO10.$name                         = "Gray_Address";
GPIO10.associatedPins.create(3);
GPIO10.associatedPins[0].$name       = "PIN_0";
GPIO10.associatedPins[0].pin.$assign = "PB0";
GPIO10.associatedPins[1].$name       = "PIN_1";
GPIO10.associatedPins[1].pin.$assign = "PB1";
GPIO10.associatedPins[2].$name       = "PIN_2";
GPIO10.associatedPins[2].pin.$assign = "PB2";

PWM1.pwmMode                            = "EDGE_ALIGN_UP";
PWM1.timerCount                         = 4000;
PWM1.$name                              = "PWM_Car";
PWM1.peripheral.$assign                 = "TIMG0";
PWM1.peripheral.ccp0Pin.$assign         = "PA12";
PWM1.peripheral.ccp1Pin.$assign         = "PB11";
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_0.dutyCycle            = 50;
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.PWM_CHANNEL_1.dutyCycle            = 50;
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";

PWM2.$name                              = "PWM_0";
PWM2.timerCount                         = 1600000;
PWM2.pwmMode                            = "EDGE_ALIGN_UP";
PWM2.peripheral.$assign                 = "TIMG12";
PWM2.peripheral.ccp0Pin.$assign         = "PB13";
PWM2.peripheral.ccp1Pin.$assign         = "PB24";
PWM2.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC2";
PWM2.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC3";
PWM2.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric8";
PWM2.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric9";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;
scripting.suppress("For best practices when the CPUCLK is running at 32MHz and above, clear the flash status bit using DL_FlashCTL_executeClearStatus\\(\\) before executing any flash operation\\. Otherwise there may be false positives\\.", SYSCTL);

TIMER1.$name              = "TIMER_Encoder_Read";
TIMER1.timerClkPrescale   = 256;
TIMER1.timerMode          = "PERIODIC";
TIMER1.interrupts         = ["ZERO"];
TIMER1.interruptPriority  = "2";
TIMER1.timerPeriod        = "20ms";
TIMER1.peripheral.$assign = "TIMG6";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
GPIO7.associatedPins[3].pin.$suggestSolution = "PB14";
