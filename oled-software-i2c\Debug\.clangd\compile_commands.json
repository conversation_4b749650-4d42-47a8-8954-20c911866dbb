[{"directory": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/sensor\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/printf\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/line\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/control\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/IMU\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MSPM0/clock.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/sensor\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/printf\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/line\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/control\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/IMU\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MSPM0/interrupt.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/sensor\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/printf\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/line\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/control\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/IMU\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_I2C/oled_software_i2c.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/sensor\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/printf\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/line\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/control\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/IMU\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/main.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/sensor\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/printf\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/line\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/control\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/IMU\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug/ti_msp_dl_config.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/sensor\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/printf\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/line\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/control\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/IMU\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MOTOR/motor.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/BNO08X_UART_RVC\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/line\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Ultrasonic_Capture\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/IMU\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/line/line.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/sensor\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/printf\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/line\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/control\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/IMU\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Delay/Delay.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/sensor\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/printf\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/line\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/control\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/IMU\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/control/control.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/sensor\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/printf\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/line\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/control\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/IMU\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/printf/printf.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/sensor\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/printf\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/line\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/control\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/IMU\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/ADC/ADC.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/sensor\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/printf\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/line\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/control\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/IMU\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/oled-software-i2c/Drivers/sensor/sensor.c"}]